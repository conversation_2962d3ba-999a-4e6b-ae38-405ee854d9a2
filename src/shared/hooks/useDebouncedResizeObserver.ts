import { RefObject, useEffect, useRef, useState } from 'react'

export const useDebouncedResizeObserver = (ref: RefObject<HTMLElement>, delay = 150) => {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })
  const resizeTimeout = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (!ref.current) return

    const observer = new ResizeObserver((entries) => {
      if (resizeTimeout.current) {
        clearTimeout(resizeTimeout.current)
      }

      resizeTimeout.current = setTimeout(() => {
        const { width, height } = entries[0].contentRect
        setDimensions({ width, height })
      }, delay)
    })

    observer.observe(ref.current)

    return () => {
      observer.disconnect()
      if (resizeTimeout.current) {
        clearTimeout(resizeTimeout.current)
      }
    }
  }, [ref, delay])

  return dimensions
}
