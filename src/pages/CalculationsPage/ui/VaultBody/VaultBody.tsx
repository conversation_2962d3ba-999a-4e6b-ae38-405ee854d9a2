import { ROLES } from 'entities/shared/roles.entities.ts'
import { ButtonMailingReport } from 'features/ButtonMailingReport'
import { observer } from 'mobx-react'
import cls from 'pages/CalculationsPage/ui/CalculationsPage.module.scss'
import { debouncedResize } from 'pages/CalculationsPage/ui/StationBody/lib'
import { ActionButton } from 'pages/CalculationsPage/ui/VaultBody/ui/ActionButton'
import { AVRCHMSpreadsheet } from 'pages/CalculationsPage/ui/VaultBody/ui/AVRCHMSpreadsheet/AVRCHMSpreadsheet.tsx'
import { VaultSpreadsheet } from 'pages/CalculationsPage/ui/VaultBody/ui/VaultSpreadsheet.tsx'
import { useEffect, useRef, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { AccessControl } from 'shared/ui/AccessControl'
import { SubtitleWithActions } from 'shared/ui/SubtitleWithActions'
import { useStore } from 'stores/useStore.ts'

import { ModalUpdateVault } from './ui/ModalUpdateVault'
import { Zones } from './ui/Zones'
import { useCollapsed } from 'app/providers/CollapsedProvider'

export const VaultBody = observer(() => {
  const { calculationsPageStore } = useStore()
  const { vaultStore, isLastDay, viewOnly, actualStage } = calculationsPageStore
  const { vaultLoadDataStatus, save, isModalUpdateVault, isEditRows, resetData } = vaultStore

  const { collapsed } = useCollapsed()

  // Refs для контейнеров таблиц
  const vaultContainerRef = useRef<HTMLDivElement | null>(null)
  const avrchmContainerRef = useRef<HTMLDivElement | null>(null)

  // Состояние для ширины контейнеров
  const [vaultWidth, setVaultWidth] = useState<number | undefined>()
  const [avrchmWidth, setAvrchmWidth] = useState<number | undefined>()

  // ResizeObserver для отслеживания изменения размера контейнеров
  useEffect(() => {
    const vaultObserver = new ResizeObserver(() => {
      debouncedResize(vaultContainerRef, () => {}, setVaultWidth, 200)
    })
    const avrchmObserver = new ResizeObserver(() => {
      debouncedResize(avrchmContainerRef, () => {}, setAvrchmWidth, 200)
    })

    if (vaultContainerRef.current) {
      vaultObserver.observe(vaultContainerRef.current)
    }
    if (avrchmContainerRef.current) {
      avrchmObserver.observe(avrchmContainerRef.current)
    }

    return () => {
      vaultObserver.disconnect()
      avrchmObserver.disconnect()
    }
  }, [collapsed])

  console.log({ vaultWidth, avrchmWidth })

  useHotkeys('ctrl+shift+s', () => !viewOnly && actualStage && isEditRows && !isLastDay && save(), {
    enableOnFormTags: true,
  })
  useHotkeys('ctrl+shift+x', () => !viewOnly && actualStage && isEditRows && !isLastDay && resetData(), {
    enableOnFormTags: true,
  })

  return (
    <div className={cls.main}>
      <SubtitleWithActions
        isActionsVisible
        title={
          <div className={cls.HeaderCustomWithButton}>
            <h2 className={cls.TitleVault}>Свод</h2>
          </div>
        }
        className={cls.title}
        actions={[
          <AccessControl key={ROLES.TECHNOLOGIST} rules={[ROLES.TECHNOLOGIST]}>
            <ActionButton.PlantsActions />
            <div className={classNames(cls.ButtonContainer, {}, [cls.dashedBorder])}>
              <ActionButton.SetFixing />
              <ActionButton.ResetFixing />
            </div>
            <div className={classNames(cls.ButtonContainer, {}, [cls.dashedBorder])}>
              <ActionButton.LoadAll />
              <ActionButton.LoadISP />
              <ActionButton.LoadMODES />
              <ActionButton.LoadCM />
            </div>
            <div className={classNames(cls.ButtonContainer, {}, [cls.dashedBorder])}>
              {isOptimization && <ActionButton.DoOptimization />}
              <ActionButton.CalculateAllowedZones />
              <ActionButton.CalculateGeneration />
              <ActionButton.EnteringAllowedZones />
              <ActionButton.EnteringAllowedZonesToBounds />
              <ActionButton.BalanceRGU />
              <ActionButton.CalculateGenerationMaximum />
            </div>
            <div className={cls.ButtonContainer}>
              <ActionButton.AVRCHMLoadButton />
            </div>
            <ActionButton.Reset />
            <ActionButton.Save />
            <div className={cls.ButtonContainer}>
              <ActionButton.Accept />
            </div>
            <div className={classNames(cls.ButtonContainer, {}, [cls.RightGap])}>
              <ActionButton.Disaccept />
            </div>
            <ButtonMailingReport plantId={0} />
          </AccessControl>,
        ]}
      />
      <div ref={vaultContainerRef} className={cls.Up} style={{ width: vaultWidth ? `${vaultWidth}px` : '100%' }}>
        <VaultSpreadsheet />
      </div>
      <div className={cls.Middle}>
        <Zones />
      </div>
      {vaultLoadDataStatus === 'DONE' && (
        <div
          ref={avrchmContainerRef}
          className={cls.DownContainer}
          style={{ width: avrchmWidth ? `${avrchmWidth}px` : '100%' }}
        >
          <AVRCHMSpreadsheet />
        </div>
      )}
      {isModalUpdateVault && <ModalUpdateVault />}
    </div>
  )
})
